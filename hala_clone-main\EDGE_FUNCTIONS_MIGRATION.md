# Edge Functions Migration Guide

This guide explains how to migrate from Next.js API routes to Supabase Edge Functions for the Hala project.

## 🚀 Overview

We've migrated the following API endpoints to Supabase Edge Functions:

### Migrated Functions

1. **Authentication Functions:**
   - `auth-login` - User login (replaces `/api/auth/login`)
   - `auth-signup` - User registration (replaces `/api/auth/signup`)
   - `auth-logout` - User logout (replaces `/api/auth/logout`)

2. **Item Management Functions:**
   - `item-mint` - Create new items (replaces `/api/nft/mint`)
   - `item-list` - Get user's items (replaces `/api/nft/my-nfts`)
   - `item-transfer` - Transfer items (replaces `/api/nft/transfer`)

3. **File Upload Functions:**
   - `upload-image` - Image upload/delete (replaces `/api/upload/image`)

4. **Analytics Functions:**
   - `dashboard-analytics` - Business analytics (replaces `/api/dashboard/analytics`)

## 📁 Project Structure

```
supabase/
├── functions/
│   ├── _shared/
│   │   └── utils.ts          # Shared utilities and types
│   ├── auth-login/
│   │   └── index.ts          # Login function
│   ├── auth-signup/
│   │   └── index.ts          # Signup function
│   ├── auth-logout/
│   │   └── index.ts          # Logout function
│   ├── item-mint/
│   │   └── index.ts          # Item creation function
│   ├── item-list/
│   │   └── index.ts          # List user items function
│   ├── item-transfer/
│   │   └── index.ts          # Transfer items function
│   ├── upload-image/
│   │   └── index.ts          # Image upload/delete function
│   └── dashboard-analytics/
│       └── index.ts          # Analytics function
└── config.toml               # Supabase configuration
```

## 🛠️ Setup and Deployment

### 1. Prerequisites

- Supabase CLI installed (`npm install -g supabase`)
- Supabase project created
- Environment variables configured

### 2. Local Development

```bash
# Start Supabase locally
npx supabase start

# Deploy functions locally
npx supabase functions deploy

# Test functions locally
npx supabase functions serve
```

### 3. Production Deployment

```bash
# Login to Supabase
npx supabase login

# Link to your project
npx supabase link --project-ref YOUR_PROJECT_REF

# Deploy all functions
npx supabase functions deploy

# Or deploy individual functions
npx supabase functions deploy auth-login
npx supabase functions deploy item-mint
# etc.
```

### 4. Environment Variables

Make sure these environment variables are set in your Supabase project:

- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_ANON_KEY` - Your Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key

## 🔧 Client-Side Integration

### Using the Edge Functions Client

```typescript
import { edgeFunctions } from '@/lib/edge-functions'

// Authentication
const loginResult = await edgeFunctions.login(email, password)
const signupResult = await edgeFunctions.signup(userData)
await edgeFunctions.logout()

// Items
const mintResult = await edgeFunctions.mintItem(itemData)
const items = await edgeFunctions.getMyItems()
await edgeFunctions.transferItem(transferData)

// File Upload
const uploadResult = await edgeFunctions.uploadImage(file, itemId)
await edgeFunctions.deleteImage(imagePath)

// Analytics
const analytics = await edgeFunctions.getAnalytics('30d')
```

### Backward Compatibility

The `edge-functions.ts` file provides backward-compatible APIs:

```typescript
import { authAPI, nftAPI, uploadAPI, analyticsAPI } from '@/lib/edge-functions'

// These work the same as before
await authAPI.login(email, password)
await nftAPI.mint(itemData)
await uploadAPI.uploadImage(file)
await analyticsAPI.getAnalytics()
```

## 🔄 Migration Steps

### Phase 1: Deploy Edge Functions
1. ✅ Created all Edge Functions
2. ✅ Set up shared utilities
3. ✅ Created client-side integration
4. 🔄 Test functions locally
5. ✅ Deploy to production

### Phase 2: Update Client Code
1. ✅ Update authentication flows
   - ✅ Updated `auth-context.tsx` logout function
2. ✅ Update item management
   - ✅ Updated `/hala-app/items/page.tsx` - fetchMyItems
   - ✅ Updated `/hala-app/business/transfers/page.tsx` - fetchMyNfts, handleTransfer
   - ✅ Updated `/hala-app/business/minting/page.tsx` - fetchMyNfts, handleSubmit, handleTransfer
   - ✅ Updated `/hala-app/business/collections/page.tsx` - fetchMyNfts
   - ✅ Updated `/hala-app/business/collections/[...]/page.tsx` - fetchMyNfts
3. ✅ Update file upload
   - ✅ Updated `lib/storage.ts` to use Edge Functions
   - ✅ ImageUpload component now uses Edge Functions via storage service
4. ✅ Update analytics
   - ✅ Updated `/hala-app/business/analytics/page.tsx` - fetchAnalytics

### Phase 3: Remove Old API Routes
1. 🔄 Remove `/app/api` routes
2. 🔄 Update any remaining references
3. 🔄 Clean up unused dependencies

## 🧪 Testing

### Local Testing

```bash
# Test authentication
curl -X POST http://127.0.0.1:54321/functions/v1/auth-login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test with authentication
curl -X GET http://127.0.0.1:54321/functions/v1/item-list \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Integration Testing

Use the provided client functions in your React components and test the full flow.

## 🚨 Important Notes

1. **Authentication**: Edge Functions use Supabase Auth tokens instead of custom JWT
2. **CORS**: All functions include proper CORS headers
3. **Error Handling**: Consistent error response format across all functions
4. **Type Safety**: Shared TypeScript types for database operations
5. **Security**: Row Level Security (RLS) policies still apply

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure your domain is added to Supabase allowed origins
2. **Authentication Errors**: Verify JWT tokens are being passed correctly
3. **Database Errors**: Check RLS policies and user permissions
4. **Function Errors**: Check Supabase function logs for detailed error messages

### Debugging

```bash
# View function logs
npx supabase functions logs auth-login

# View all function logs
npx supabase functions logs
```

## 📈 Benefits

1. **Performance**: Edge Functions run closer to users
2. **Scalability**: Automatic scaling with Supabase
3. **Security**: Built-in authentication and RLS
4. **Monitoring**: Built-in logging and analytics
5. **Cost**: Pay-per-execution model
6. **Maintenance**: No server management required

## 🎯 Next Steps

1. Complete local testing of all functions
2. Deploy to production Supabase project
3. Update client-side code to use Edge Functions
4. Remove old Next.js API routes
5. Monitor performance and errors
6. Optimize based on usage patterns
