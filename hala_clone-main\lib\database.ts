import { supabaseAdmin } from './supabase'
import { serverStorageService, imageUtils } from './storage'
import type { UserProfile, UserSession } from './models/user'
import type { Item, NFT, OwnershipRecord, Transfer } from './models/nft'

// Profile operations (using Supabase Auth + profiles table)
export const profileService = {
  async findByEmail(email: string): Promise<UserProfile | null> {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single()

    if (error) return null
    return data
  },

  async create(profileData: Omit<UserProfile, 'created_at' | 'updated_at'>): Promise<UserProfile> {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .insert({
        ...profileData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  async findById(id: string): Promise<UserProfile | null> {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) return null
    return data
  },

  async update(id: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async countAll(): Promise<number> {
    const { count, error } = await supabaseAdmin
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    if (error) throw error
    return count || 0
  },

  async countByRole(role: string): Promise<number> {
    const { count, error } = await supabaseAdmin
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('role', role)

    if (error) throw error
    return count || 0
  }
}

// For backward compatibility
export const userService = profileService

// Item operations
export const itemService = {
  async create(itemData: Omit<Item, 'id' | 'created_at'>): Promise<Item> {
    const { data, error } = await supabaseAdmin
      .from('items')
      .insert(itemData)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async findById(id: string): Promise<Item | null> {
    const { data, error } = await supabaseAdmin
      .from('items')
      .select(`
        *,
        ownership_history (
          id,
          user_id,
          user_name,
          transferred_at,
          transferred_from,
          transferred_from_name
        )
      `)
      .eq('id', id)
      .single()
    
    if (error) return null
    return data
  },

  async findByOwnerId(ownerId: string, isActive: boolean = true): Promise<Item[]> {
    const { data, error } = await supabaseAdmin
      .from('items')
      .select('*')
      .eq('owner_id', ownerId)
      .eq('is_active', isActive)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  async findByCreatorId(creatorId: string, isActive: boolean = true): Promise<Item[]> {
    const { data, error } = await supabaseAdmin
      .from('items')
      .select('*')
      .eq('creator_id', creatorId)
      .eq('is_active', isActive)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  async updateOwner(itemId: string, newOwnerId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('items')
      .update({ owner_id: newOwnerId })
      .eq('id', itemId)
    
    if (error) throw error
  },

  async countByCreatorId(creatorId: string, isActive: boolean = true): Promise<number> {
    const { count, error } = await supabaseAdmin
      .from('items')
      .select('*', { count: 'exact', head: true })
      .eq('creator_id', creatorId)
      .eq('is_active', isActive)

    if (error) throw error
    return count || 0
  },

  async deleteItem(itemId: string): Promise<void> {
    // First get the item to access the image URL
    const item = await this.findById(itemId)

    if (item && item.image_url) {
      // Extract storage path from URL and delete image
      const imagePath = imageUtils.extractPathFromUrl(item.image_url)
      if (imagePath) {
        try {
          await serverStorageService.deleteImage(imagePath)
        } catch (error) {
          console.warn('Failed to delete image from storage:', error)
          // Continue with item deletion even if image deletion fails
        }
      }
    }

    // Delete the item from database
    const { error } = await supabaseAdmin
      .from('items')
      .delete()
      .eq('id', itemId)

    if (error) throw error
  }
}

// Ownership history operations
export const ownershipService = {
  async addRecord(record: Omit<OwnershipRecord, 'id' | 'transferred_at'>): Promise<void> {
    const { error } = await supabaseAdmin
      .from('ownership_history')
      .insert(record)
    
    if (error) throw error
  }
}

// Transfer operations
export const transferService = {
  async create(transferData: Omit<Transfer, 'id' | 'transferred_at'>): Promise<Transfer> {
    const { data, error } = await supabaseAdmin
      .from('transfers')
      .insert(transferData)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async findBySenderId(senderId: string): Promise<Transfer[]> {
    const { data, error } = await supabaseAdmin
      .from('transfers')
      .select('*')
      .eq('sender_id', senderId)
      .order('transferred_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  async findByUserId(userId: string): Promise<Transfer[]> {
    const { data, error } = await supabaseAdmin
      .from('transfers')
      .select('*')
      .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
      .order('transferred_at', { ascending: false })
      .limit(5)
    
    if (error) throw error
    return data || []
  },

  async findByRecipientId(recipientId: string, startDate?: Date): Promise<Transfer[]> {
    let query = supabaseAdmin
      .from('transfers')
      .select('*')
      .eq('recipient_id', recipientId)
    
    if (startDate) {
      query = query.gte('transferred_at', startDate.toISOString())
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data || []
  },

  async findByItemIds(itemIds: string[], startDate?: Date): Promise<Transfer[]> {
    let query = supabaseAdmin
      .from('transfers')
      .select('*')
      .in('item_id', itemIds)
    
    if (startDate) {
      query = query.gte('transferred_at', startDate.toISOString())
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data || []
  }
}

// Utility functions for backward compatibility with MongoDB ObjectId format
export const convertToNFTFormat = (item: Item): NFT => {
  return {
    ...item,
    _id: item.id,
    imageUrl: item.image_url,
    serialNumber: item.serial_number,
    createdAt: item.created_at,
    creatorId: item.creator_id,
    ownerId: item.owner_id,
    ownershipHistory: item.ownership_history || [],
    isActive: item.is_active
  }
}

export const convertFromNFTFormat = (nft: Partial<NFT>): Partial<Item> => {
  return {
    id: nft._id || nft.id,
    name: nft.name,
    description: nft.description,
    image_url: nft.imageUrl || nft.image_url,
    brand: nft.brand,
    year: nft.year,
    serial_number: nft.serialNumber || nft.serial_number,
    created_at: nft.createdAt || nft.created_at,
    creator_id: nft.creatorId || nft.creator_id,
    owner_id: nft.ownerId || nft.owner_id,
    is_active: nft.isActive !== undefined ? nft.isActive : nft.is_active
  }
}
