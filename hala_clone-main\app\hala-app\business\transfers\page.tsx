"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { PageTransition } from "@/components/page-transition"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Send, Search } from "lucide-react"
import { SuccessAnimation } from "@/components/success-animation"

interface NFT {
  _id: string
  name: string
  imageUrl: string
  description: string
  brand?: string
  serialNumber?: string
}

interface TransferHistory {
  _id: string
  nftId: string
  nftName: string
  recipientEmail: string
  transferredAt: string
  status: string
}

export default function BusinessTransfers() {
  const [nfts, setNfts] = useState<NFT[]>([])
  const [selectedNft, setSelectedNft] = useState<NFT | null>(null)
  const [recipientEmail, setRecipientEmail] = useState("")
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isTransferring, setIsTransferring] = useState(false)
  const [transferHistory, setTransferHistory] = useState<TransferHistory[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const { toast } = useToast()

  useEffect(() => {
    fetchMyNfts()
    fetchTransferHistory()
  }, [])

  const fetchMyNfts = async () => {
    setIsLoading(true)
    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const data = await edgeFunctions.getMyItems()

      if (data.success) {
        setNfts(data.nfts)
      } else {
        throw new Error(data.error || "Failed to fetch NFTs")
      }
    } catch (error) {
      console.error("Error fetching NFTs:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFTs",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchTransferHistory = async () => {
    try {
      const response = await fetch("/api/nft/transfer-history")
      const data = await response.json()

      if (data.success) {
        setTransferHistory(data.transfers)
      } else {
        throw new Error(data.message || "Failed to fetch transfer history")
      }
    } catch (error) {
      console.error("Error fetching transfer history:", error)
    }
  }

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedNft) {
      toast({
        title: "Error",
        description: "Please select an NFT to transfer",
        variant: "destructive",
      })
      return
    }

    if (!recipientEmail) {
      toast({
        title: "Error",
        description: "Please enter a recipient email",
        variant: "destructive",
      })
      return
    }

    setIsTransferring(true)

    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const data = await edgeFunctions.transferItem({
        nftId: selectedNft._id,
        recipientEmail,
        message,
      })

      if (data.success) {
        setSuccessMessage(`Token "${selectedNft.name}" transferred successfully to ${recipientEmail}!`)
        setShowSuccessAnimation(true)

        setSelectedNft(null)
        setRecipientEmail("")
        setMessage("")

        fetchMyNfts()
        fetchTransferHistory()
      } else {
        throw new Error(data.error || "Failed to transfer NFT")
      }
    } catch (error) {
      console.error("Error transferring NFT:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to transfer NFT",
        variant: "destructive",
      })
    } finally {
      setIsTransferring(false)
    }
  }

  const handleAnimationComplete = () => {
    setShowSuccessAnimation(false)
  }

  const filteredHistory = transferHistory.filter(
    (transfer) =>
      transfer.nftName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.recipientEmail.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <PageTransition>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-light">Transfer Tokens</h1>
          <p className="text-gray-500 mt-2 text-sm md:text-base">Send tokens to your customers via email</p>
        </div>

        <Tabs defaultValue="transfer" className="space-y-6">
          <TabsList>
            <TabsTrigger value="transfer">Transfer Token</TabsTrigger>
            <TabsTrigger value="history">Transfer History</TabsTrigger>
          </TabsList>

          <TabsContent value="transfer">
            <Card className="p-6 md:p-8 max-w-3xl mx-auto">
              <form onSubmit={handleTransfer} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="nft">Select Token</Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                    {isLoading ? (
                      <div className="col-span-full flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                      </div>
                    ) : nfts.length === 0 ? (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        No tokens available for transfer. Create some first!
                      </div>
                    ) : (
                      nfts.map((nft) => (
                        <div
                          key={nft._id}
                          className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                            selectedNft?._id === nft._id
                              ? "border-black bg-gray-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedNft(nft)}
                        >
                          <div className="aspect-square rounded-md overflow-hidden bg-gray-100 mb-2">
                            <img
                              src={nft.imageUrl || "/placeholder.svg"}
                              alt={nft.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <h3 className="font-medium text-sm truncate">{nft.name}</h3>
                          {nft.serialNumber && <p className="text-xs text-gray-500 truncate">#{nft.serialNumber}</p>}
                        </div>
                      ))
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recipientEmail">Recipient Email</Label>
                  <Input
                    id="recipientEmail"
                    type="email"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message (Optional)</Label>
                  <textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    rows={3}
                    className="w-full rounded-md border border-gray-200 p-2 md:p-3 text-sm md:text-base focus:border-black focus:ring-black transition-colors"
                    placeholder="Add a personal message to the recipient..."
                  />
                </div>

                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
                    disabled={isTransferring || !selectedNft}
                  >
                    {isTransferring ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Transferring...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Transfer Token
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card className="p-6 md:p-8 max-w-3xl mx-auto">
              <div className="space-y-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by token name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-black focus:ring-black transition-colors"
                  />
                </div>

                <div className="space-y-4">
                  {filteredHistory.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">No transfer history found.</div>
                  ) : (
                    filteredHistory.map((transfer) => (
                      <div key={transfer._id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{transfer.nftName}</h3>
                            <p className="text-sm text-gray-500">Sent to: {transfer.recipientEmail}</p>
                            <p className="text-xs text-gray-400 mt-1">
                              {new Date(transfer.transferredAt).toLocaleString()}
                            </p>
                          </div>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              transfer.status === "completed"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }`}
                          >
                            {transfer.status === "completed" ? "Completed" : "Pending"}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <SuccessAnimation
        show={showSuccessAnimation}
        message={successMessage}
        onComplete={handleAnimationComplete}
        duration={3000}
      />
    </PageTransition>
  )
}
