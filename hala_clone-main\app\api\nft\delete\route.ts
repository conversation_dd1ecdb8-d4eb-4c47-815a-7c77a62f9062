import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { itemService } from '@/lib/database'

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const user = await authService.getUserSession()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { itemId } = await request.json()

    if (!itemId) {
      return NextResponse.json({ error: 'Item ID is required' }, { status: 400 })
    }

    // Verify user owns this item
    const item = await itemService.findById(itemId)
    
    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 })
    }

    if (item.owner_id !== user.id) {
      return NextResponse.json({ error: 'You can only delete items you own' }, { status: 403 })
    }

    // Delete the item (this will also clean up the image)
    await itemService.deleteItem(itemId)

    return NextResponse.json({
      success: true,
      message: 'Item deleted successfully'
    })

  } catch (error) {
    console.error('Item deletion error:', error)
    return NextResponse.json(
      { 
        error: 'Delete failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
