"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { CheckCircle2, Loader2, Send } from "lucide-react"
import { PageTransition } from "@/components/page-transition"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { SuccessAnimation } from "@/components/success-animation"
import { ImageUpload } from "@/components/ui/image-upload"

interface NFT {
  _id: string
  name: string
  imageUrl: string
  description: string
  brand?: string
  serialNumber?: string
}

export default function BusinessMinting() {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [imagePath, setImagePath] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    brand: "HALA",
    year: new Date().getFullYear().toString(),
    serialNumber: "",
  })
  const [nfts, setNfts] = useState<NFT[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedNft, setSelectedNft] = useState<NFT | null>(null)
  const [recipientEmail, setRecipientEmail] = useState("")
  const [message, setMessage] = useState("")
  const [isTransferring, setIsTransferring] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleImageUploaded = (url: string, path: string) => {
    setImageUrl(url)
    setImagePath(path)
  }

  const handleImageRemoved = () => {
    setImageUrl("")
    setImagePath("")
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const fetchMyNfts = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/nft/my-nfts")
      const data = await response.json()

      if (data.success) {
        setNfts(data.nfts)
      } else {
        throw new Error(data.message || "Failed to fetch NFTs")
      }
    } catch (error) {
      console.error("Error fetching NFTs:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFTs",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!imageUrl) {
      toast({
        title: "Error",
        description: "Please upload an image for the item",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch("/api/nft/mint", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          imageUrl,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setIsSuccess(true)
        setSuccessMessage(`Your token "${formData.name}" has been created successfully!`)
        setShowSuccessAnimation(true)

        setTimeout(() => {
          setIsSuccess(false)
          setImageUrl("")
          setImagePath("")
          setFormData({
            name: "",
            description: "",
            brand: "HALA",
            year: new Date().getFullYear().toString(),
            serialNumber: "",
          })

          fetchMyNfts()
        }, 3000)
      } else {
        throw new Error(data.message || "Failed to create NFT")
      }
    } catch (error) {
      console.error("Error creating NFT:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create NFT",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsTransferring(true)

    try {
      const response = await fetch("/api/nft/transfer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          nftId: selectedNft?._id,
          recipientEmail,
          message,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setSuccessMessage(`Token "${selectedNft?.name}" transferred successfully to ${recipientEmail}!`)
        setShowSuccessAnimation(true)

        setSelectedNft(null)
        setRecipientEmail("")
        setMessage("")

        fetchMyNfts()
      } else {
        throw new Error(data.message || "Failed to transfer NFT")
      }
    } catch (error) {
      console.error("Error transferring NFT:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to transfer NFT",
        variant: "destructive",
      })
    } finally {
      setIsTransferring(false)
    }
  }

  const handleAnimationComplete = () => {
    setShowSuccessAnimation(false)
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-light">Create Token</h1>
          <p className="text-gray-500 mt-2 text-sm md:text-base">Tokenize your products and send them to customers</p>
        </div>

        <Tabs defaultValue="create" className="space-y-6">
          <TabsList>
            <TabsTrigger value="create">Create Token</TabsTrigger>
            <TabsTrigger value="transfer" onClick={fetchMyNfts}>
              Transfer Token
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create">
            <Card className="p-6 md:p-8 max-w-3xl mx-auto">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="E.g. HALA Cap - Black"
                    required
                    className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="brand">Brand</Label>
                  <Input
                    id="brand"
                    name="brand"
                    value={formData.brand}
                    onChange={handleInputChange}
                    placeholder="E.g. HALA"
                    required
                    className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="year">Year</Label>
                    <Input
                      id="year"
                      name="year"
                      value={formData.year}
                      onChange={handleInputChange}
                      placeholder="E.g. 2023"
                      className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="serialNumber">Serial Number</Label>
                    <Input
                      id="serialNumber"
                      name="serialNumber"
                      value={formData.serialNumber}
                      onChange={handleInputChange}
                      placeholder="E.g. HALA123456"
                      className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full rounded-md border border-gray-200 p-2 md:p-3 text-sm md:text-base focus:border-black focus:ring-black transition-colors"
                    placeholder="Describe the product in detail..."
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Upload Image</Label>
                  <ImageUpload
                    onImageUploaded={handleImageUploaded}
                    onImageRemoved={handleImageRemoved}
                    currentImageUrl={imageUrl}
                    disabled={isSubmitting}
                  />
                </div>

                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
                    disabled={isSubmitting || isSuccess || !imageUrl}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : isSuccess ? (
                      <>
                        <CheckCircle2 className="h-5 w-5 mr-2" />
                        Token created successfully!
                      </>
                    ) : (
                      "Create Token"
                    )}
                  </Button>
                </div>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="transfer">
            <Card className="p-6 md:p-8 max-w-3xl mx-auto">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Select Token to Transfer</Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                    {isLoading ? (
                      <div className="col-span-full flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                      </div>
                    ) : nfts.length === 0 ? (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        No tokens available for transfer. Create some first!
                      </div>
                    ) : (
                      nfts.map((nft) => (
                        <div
                          key={nft._id}
                          className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                            selectedNft?._id === nft._id
                              ? "border-black bg-gray-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedNft(nft)}
                        >
                          <div className="aspect-square rounded-md overflow-hidden bg-gray-100 mb-2">
                            <img
                              src={nft.imageUrl || "/placeholder.svg"}
                              alt={nft.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <h3 className="font-medium text-sm truncate">{nft.name}</h3>
                          {nft.serialNumber && <p className="text-xs text-gray-500 truncate">#{nft.serialNumber}</p>}
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {selectedNft && (
                  <div className="space-y-4 pt-4 border-t border-gray-100">
                    <div className="space-y-2">
                      <Label htmlFor="recipientEmail">Recipient Email</Label>
                      <Input
                        id="recipientEmail"
                        type="email"
                        value={recipientEmail}
                        onChange={(e) => setRecipientEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        required
                        className="border-gray-200 focus:border-black focus:ring-black transition-colors"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Message (Optional)</Label>
                      <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        rows={3}
                        className="w-full rounded-md border border-gray-200 p-2 md:p-3 text-sm md:text-base focus:border-black focus:ring-black transition-colors"
                        placeholder="Add a personal message to the recipient..."
                      />
                    </div>

                    <Button
                      onClick={handleTransfer}
                      className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
                      disabled={isTransferring || !recipientEmail}
                    >
                      {isTransferring ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Transferring...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Transfer Token
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <SuccessAnimation
        show={showSuccessAnimation}
        message={successMessage}
        onComplete={handleAnimationComplete}
        duration={3000}
      />
    </PageTransition>
  )
}
